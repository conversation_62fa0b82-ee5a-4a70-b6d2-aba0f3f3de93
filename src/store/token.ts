import { makeAutoObservable } from "mobx";
import axios from "axios";
import token from "@/types/token";

export class TokenStore {
  // Token 列表缓存
  tokens: token[] = [];
  // 缓存时间戳
  cacheTimestamp: number = 0;
  // 缓存有效期（1小时 = 3600000毫秒）
  private readonly CACHE_DURATION = 3600000;

  constructor() {
    makeAutoObservable(this);
  }

  /**
   * 获取 Token 列表
   * @param chainId 源链ID，默认为4689
   * @param destChainId 目标链ID，默认为1
   * @returns Promise<token[]>
   */
  async getTokenList(chainId: number = 4689, destChainId: number = 1): Promise<token[]> {
    const now = Date.now();

    // 检查缓存是否有效（1小时内）
    if (this.tokens.length > 0 && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      return this.tokens;
    }

    try {
      // 发起 axios 请求
      const response = await axios.get(`http://localhost:9527/tube/getTokenList`, {
        params: {
          chainId,
          destChainId
        }
      });

      // 更新缓存
      this.tokens = response.data || [];
      this.cacheTimestamp = now;

      return this.tokens;
    } catch (error) {
      console.error('Failed to fetch token list:', error);
      // 如果请求失败但有缓存数据，返回缓存数据
      if (this.tokens.length > 0) {
        return this.tokens;
      }
      // 否则返回空数组
      return [];
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.tokens = [];
    this.cacheTimestamp = 0;
  }

  /**
   * 检查缓存是否有效
   */
  get isCacheValid(): boolean {
    const now = Date.now();
    return this.tokens.length > 0 && (now - this.cacheTimestamp) < this.CACHE_DURATION;
  }
}
