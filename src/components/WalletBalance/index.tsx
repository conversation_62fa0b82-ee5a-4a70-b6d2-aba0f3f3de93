import { useWalletBalance } from "@/hooks/useWalletBalance";
import type { UseWalletBalanceOptions } from "@/hooks/useWalletBalance";
import { cn } from "@/lib/utils/util";
import { Spinner } from "@heroui/spinner";
import { Tooltip } from "@heroui/tooltip";

// 刷新图标组件
const RefreshIcon = ({ size = 12, className = "" }: { size?: number; className?: string }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
    <path d="M21 3v5h-5" />
    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
    <path d="M3 21v-5h5" />
  </svg>
);

interface WalletBalanceProps extends UseWalletBalanceOptions {
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示刷新按钮 */
  showRefreshButton?: boolean;
  /** 是否显示代币符号 */
  showSymbol?: boolean;
  /** 是否显示加载动画 */
  showLoadingSpinner?: boolean;
  /** 错误时的回退文本 */
  errorFallback?: string;
  /** 加载时的回退文本 */
  loadingFallback?: string;
  /** 自定义格式化函数 */
  customFormatter?: (balance: string, symbol: string) => string;
  /** 点击事件处理 */
  onClick?: () => void;
}

/**
 * 钱包余额显示组件
 * 支持自动刷新、错误处理、加载状态等功能
 * 使用新的格式化规则
 */
export function WalletBalance({
  className,
  showRefreshButton = false,
  showSymbol = true,
  showLoadingSpinner = false,
  errorFallback = "Error",
  loadingFallback = "Loading...",
  customFormatter,
  onClick,
  ...balanceOptions
}: WalletBalanceProps) {
  const {
    formattedBalance,
    formattedBalanceWithSymbol,
    nativeTokenSymbol,
    isLoading,
    error,
    hasBalance,
    isZeroBalance,
    refetch,
  } = useWalletBalance(balanceOptions);

  const handleRefresh = () => {
    refetch();
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <span className="flex items-center gap-1">
          {showLoadingSpinner && <Spinner size="sm" />}
          {loadingFallback}
        </span>
      );
    }

    if (error) {
      return (
        <Tooltip content={error.message}>
          <span className="text-red-500 cursor-help">
            {errorFallback}
          </span>
        </Tooltip>
      );
    }

    const balanceText = showSymbol 
      ? formattedBalanceWithSymbol
      : formattedBalance;

    const finalText = customFormatter 
      ? customFormatter(formattedBalance, nativeTokenSymbol)
      : balanceText;

    return (
      <span className={cn(
        isZeroBalance && "text-gray-500",
        "transition-colors duration-200"
      )}>
        {finalText}
      </span>
    );
  };

  return (
    <div className={cn(
      "flex items-center gap-2",
      onClick && "cursor-pointer hover:opacity-80 transition-opacity",
      className
    )} onClick={onClick}>
      {renderContent()}
      
      {showRefreshButton && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleRefresh();
          }}
          className={cn(
            "p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",
            "transition-colors duration-200",
            isLoading && "animate-spin"
          )}
          disabled={isLoading}
          title="刷新余额"
        >
          <RefreshIcon size={12} />
        </button>
      )}
    </div>
  );
}

/**
 * 简化的余额显示组件，用于 NavBar 等场景
 */
export function SimpleWalletBalance({ 
  className,
  ...props 
}: Omit<WalletBalanceProps, 'showRefreshButton' | 'showLoadingSpinner'>) {
  return (
    <WalletBalance
      className={cn("text-sm font-medium text-purple1", className)}
      showRefreshButton={false}
      showLoadingSpinner={false}
      {...props}
    />
  );
}

/**
 * 带刷新功能的余额显示组件
 */
export function RefreshableWalletBalance({ 
  className,
  ...props 
}: WalletBalanceProps) {
  return (
    <WalletBalance
      className={cn("text-sm font-medium", className)}
      showRefreshButton={true}
      showLoadingSpinner={true}
      {...props}
    />
  );
}

/**
 * 专门用于小额代币的余额显示组件
 * 显示更多有效数字，适合 DeFi 场景
 */
export function SmallTokenBalance({ 
  className,
  ...props 
}: WalletBalanceProps) {
  return (
    <WalletBalance
      className={cn("text-sm font-medium", className)}
      formatOptions={{
        significantDigitsForSmall: 6, // 显示6位有效数字
        decimalPlacesForLarge: 2,     // 大额时只显示2位小数
        useThousandsSeparator: true,
      }}
      {...props}
    />
  );
}

/**
 * 专门用于稳定币的余额显示组件
 * 通常只显示2位小数
 */
export function StablecoinBalance({ 
  className,
  ...props 
}: WalletBalanceProps) {
  return (
    <WalletBalance
      className={cn("text-sm font-medium", className)}
      formatOptions={{
        significantDigitsForSmall: 4,
        decimalPlacesForLarge: 2,     // 稳定币只显示2位小数
        useThousandsSeparator: true,
      }}
      {...props}
    />
  );
}

/**
 * 高精度余额显示组件
 * 用于需要显示完整精度的场景
 */
export function HighPrecisionBalance({ 
  className,
  ...props 
}: WalletBalanceProps) {
  return (
    <WalletBalance
      className={cn("text-sm font-medium font-mono", className)}
      formatOptions={{
        significantDigitsForSmall: 8, // 显示8位有效数字
        decimalPlacesForLarge: 8,     // 显示8位小数
        useThousandsSeparator: true,
      }}
      {...props}
    />
  );
}
