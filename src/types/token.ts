import "reflect-metadata";

export default class token {
  public id?: number;
  public chainid?: number;
  public dest_chainid?: number;
  public address?: string;
  public ctoken_address?: string;
  public dest_address?: string;
  public name?: string;
  public decimals?: number;
  public symbol?: string;
  public logouri?: string;
  public cashier?: string;
  public quick_swap_from?: string;
  public quick_swap?: string;
  public token_only_dest_wrapper?: string;
  public need_unwrapper?: boolean;
  public is_popular?: boolean;
  public is_depintoken?: boolean;
  public is_wrapped?: boolean;
  public createat?: string;
  public updateat?: string;
  public router_address?: string;
  public usdc_dest_wrapper?: string;
  public ctoken?: string;
}