import React, { useState } from 'react';
import {
  useBalance,
  useReadContract,
  useWriteContract,
  useWaitForTransactionReceipt,
  useAccount,
  useChainId,
  useSwitchChain,
  useBlockNumber,
  useTransaction,
  useContractRead,
  useSimulateContract,
} from 'wagmi';
import { parseEther, formatEther, parseUnits, formatUnits } from 'viem';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { Card, CardBody, CardHeader } from '@heroui/card';

// ERC20 ABI 示例
const ERC20_ABI = [
  {
    inputs: [{ name: 'account', type: 'address' }],
    name: 'balanceOf',
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      { name: 'to', type: 'address' },
      { name: 'amount', type: 'uint256' },
    ],
    name: 'transfer',
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { name: 'spender', type: 'address' },
      { name: 'amount', type: 'uint256' },
    ],
    name: 'approve',
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'symbol',
    outputs: [{ name: '', type: 'string' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

export function WagmiExamples() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { switchChain } = useSwitchChain();
  
  // 状态管理
  const [tokenAddress, setTokenAddress] = useState('0xA0b86a33E6441b8dB8C7C8b8C7C8b8C7C8b8C7C8'); // 示例代币地址
  const [transferTo, setTransferTo] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [approveSpender, setApproveSpender] = useState('');
  const [approveAmount, setApproveAmount] = useState('');

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Wagmi 使用示例</h1>
      
      {/* 1. 查询原生代币余额 */}
      <BalanceExample address={address} />
      
      {/* 2. 查询 ERC20 代币余额 */}
      <TokenBalanceExample 
        address={address} 
        tokenAddress={tokenAddress}
        onTokenAddressChange={setTokenAddress}
      />
      
      {/* 3. 执行 ERC20 转账 */}
      <TransferExample 
        tokenAddress={tokenAddress}
        transferTo={transferTo}
        transferAmount={transferAmount}
        onTransferToChange={setTransferTo}
        onTransferAmountChange={setTransferAmount}
      />
      
      {/* 4. 执行 ERC20 授权 */}
      <ApproveExample 
        tokenAddress={tokenAddress}
        approveSpender={approveSpender}
        approveAmount={approveAmount}
        onApproveSpenderChange={setApproveSpender}
        onApproveAmountChange={setApproveAmount}
      />
      
      {/* 5. 网络切换 */}
      <NetworkExample chainId={chainId} switchChain={switchChain} />
      
      {/* 6. 区块信息 */}
      <BlockInfoExample />
    </div>
  );
}

// 1. 原生代币余额查询
function BalanceExample({ address }: { address?: string }) {
  const { data: balance, isLoading, error, refetch } = useBalance({
    address: address as `0x${string}`,
    query: {
      enabled: !!address,
      refetchInterval: 30000, // 30秒自动刷新
    },
  });

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">原生代币余额</h3>
      </CardHeader>
      <CardBody>
        {isLoading && <p>加载中...</p>}
        {error && <p className="text-red-500">错误: {error.message}</p>}
        {balance && (
          <div>
            <p>余额: {formatEther(balance.value)} {balance.symbol}</p>
            <p>小数位: {balance.decimals}</p>
            <Button size="sm" onClick={() => refetch()}>刷新</Button>
          </div>
        )}
      </CardBody>
    </Card>
  );
}

// 2. ERC20 代币余额查询
function TokenBalanceExample({ 
  address, 
  tokenAddress, 
  onTokenAddressChange 
}: { 
  address?: string;
  tokenAddress: string;
  onTokenAddressChange: (value: string) => void;
}) {
  // 查询代币余额
  const { data: balance, isLoading: balanceLoading } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [address as `0x${string}`],
    query: {
      enabled: !!address && !!tokenAddress,
    },
  });

  // 查询代币信息
  const { data: symbol } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'symbol',
  });

  const { data: decimals } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'decimals',
  });

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">ERC20 代币余额</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <Input
          label="代币合约地址"
          value={tokenAddress}
          onChange={(e) => onTokenAddressChange(e.target.value)}
          placeholder="0x..."
        />
        
        {balanceLoading && <p>查询中...</p>}
        {balance && decimals && (
          <div>
            <p>代币: {symbol}</p>
            <p>余额: {formatUnits(balance as bigint, decimals)} {symbol}</p>
            <p>小数位: {decimals}</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
}

// 3. ERC20 转账示例
function TransferExample({
  tokenAddress,
  transferTo,
  transferAmount,
  onTransferToChange,
  onTransferAmountChange,
}: {
  tokenAddress: string;
  transferTo: string;
  transferAmount: string;
  onTransferToChange: (value: string) => void;
  onTransferAmountChange: (value: string) => void;
}) {
  // 获取代币小数位
  const { data: decimals } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'decimals',
  });

  // 模拟交易（可选，用于预估 gas）
  const { data: simulateData } = useSimulateContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'transfer',
    args: [
      transferTo as `0x${string}`,
      decimals ? parseUnits(transferAmount || '0', decimals) : 0n,
    ],
    query: {
      enabled: !!transferTo && !!transferAmount && !!decimals,
    },
  });

  // 执行转账
  const { 
    writeContract, 
    data: hash, 
    isPending, 
    error 
  } = useWriteContract();

  // 等待交易确认
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const handleTransfer = () => {
    if (!decimals || !transferTo || !transferAmount) return;
    
    writeContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: 'transfer',
      args: [
        transferTo as `0x${string}`,
        parseUnits(transferAmount, decimals),
      ],
    });
  };

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">ERC20 转账</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <Input
          label="接收地址"
          value={transferTo}
          onChange={(e) => onTransferToChange(e.target.value)}
          placeholder="0x..."
        />
        
        <Input
          label="转账数量"
          value={transferAmount}
          onChange={(e) => onTransferAmountChange(e.target.value)}
          placeholder="0.0"
          type="number"
        />
        
        <Button 
          onClick={handleTransfer}
          disabled={isPending || isConfirming || !transferTo || !transferAmount}
          color={isSuccess ? 'success' : 'primary'}
        >
          {isPending ? '准备中...' : isConfirming ? '确认中...' : isSuccess ? '转账成功' : '转账'}
        </Button>
        
        {error && (
          <p className="text-red-500">错误: {error.message}</p>
        )}
        
        {hash && (
          <p className="text-sm">交易哈希: {hash}</p>
        )}
      </CardBody>
    </Card>
  );
}

// 4. ERC20 授权示例
function ApproveExample({
  tokenAddress,
  approveSpender,
  approveAmount,
  onApproveSpenderChange,
  onApproveAmountChange,
}: {
  tokenAddress: string;
  approveSpender: string;
  approveAmount: string;
  onApproveSpenderChange: (value: string) => void;
  onApproveAmountChange: (value: string) => void;
}) {
  // 获取代币小数位
  const { data: decimals } = useReadContract({
    address: tokenAddress as `0x${string}`,
    abi: ERC20_ABI,
    functionName: 'decimals',
  });

  // 执行授权
  const {
    writeContract,
    data: hash,
    isPending,
    error
  } = useWriteContract();

  // 等待交易确认
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const handleApprove = () => {
    if (!decimals || !approveSpender || !approveAmount) return;

    writeContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: 'approve',
      args: [
        approveSpender as `0x${string}`,
        parseUnits(approveAmount, decimals),
      ],
    });
  };

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">ERC20 授权</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        <Input
          label="授权地址"
          value={approveSpender}
          onChange={(e) => onApproveSpenderChange(e.target.value)}
          placeholder="0x..."
        />

        <Input
          label="授权数量"
          value={approveAmount}
          onChange={(e) => onApproveAmountChange(e.target.value)}
          placeholder="0.0"
          type="number"
        />

        <Button
          onClick={handleApprove}
          disabled={isPending || isConfirming || !approveSpender || !approveAmount}
          color={isSuccess ? 'success' : 'primary'}
        >
          {isPending ? '准备中...' : isConfirming ? '确认中...' : isSuccess ? '授权成功' : '授权'}
        </Button>

        {error && (
          <p className="text-red-500">错误: {error.message}</p>
        )}

        {hash && (
          <p className="text-sm">交易哈希: {hash}</p>
        )}
      </CardBody>
    </Card>
  );
}

// 5. 网络切换示例
function NetworkExample({
  chainId,
  switchChain
}: {
  chainId: number;
  switchChain: any;
}) {
  const networks = [
    { id: 1, name: 'Ethereum' },
    { id: 56, name: 'BSC' },
    { id: 137, name: 'Polygon' },
    { id: 4689, name: 'IoTeX' },
  ];

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">网络切换</h3>
      </CardHeader>
      <CardBody>
        <p>当前网络 ID: {chainId}</p>
        <div className="flex gap-2 mt-4">
          {networks.map((network) => (
            <Button
              key={network.id}
              size="sm"
              variant={chainId === network.id ? 'solid' : 'bordered'}
              onClick={() => switchChain({ chainId: network.id })}
            >
              {network.name}
            </Button>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}

// 6. 区块信息示例
function BlockInfoExample() {
  const { data: blockNumber, isLoading } = useBlockNumber({
    query: {
      refetchInterval: 12000, // 每12秒刷新
    },
  });

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">区块信息</h3>
      </CardHeader>
      <CardBody>
        {isLoading ? (
          <p>加载中...</p>
        ) : (
          <p>当前区块高度: {blockNumber?.toString()}</p>
        )}
      </CardBody>
    </Card>
  );
}
        )}
      </CardBody>
    </Card>
  );
}
